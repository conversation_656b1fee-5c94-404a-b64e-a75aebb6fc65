"""
通过 TinyCLIP 计算图片特征向量
"""

import torch
from PIL import Image
from transformers import CLIPModel, CLIPProcessor

MODEL_NAME = 'wkcn/TinyCLIP-ViT-61M-32-Text-29M-LAION400M'


def get_device():
    if torch.cuda.is_available():
        return torch.device('cuda')
    elif torch.backends.mps.is_available():
        return torch.device('mps')
    else:
        return torch.device('cpu')


def load_model(model_name: str) -> tuple[CLIPModel, CLIPProcessor]:
    """加载模型"""
    model = CLIPModel.from_pretrained(model_name)
    processor = CLIPProcessor.from_pretrained(model_name, use_fast=True)
    return model, processor


def embed_images(images: list[Image.Image]):
    """计算图片特征向量"""
