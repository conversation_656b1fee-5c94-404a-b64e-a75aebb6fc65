[project]
name = "ifinder"
version = "0.1.0"
description = "Image finder for searching images by image."
readme = "README.md"
authors = [
    { name = "Seamile", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "hnswlib>=0.8.0",
    "pillow>=11.3.0",
    "transformers>=4.55.4",
]

[project.scripts]
ifinder = "ifinder:main"

[build-system]
requires = ["uv_build>=0.8.6,<0.9.0"]
build-backend = "uv_build"

[dependency-groups]
dev = [
    "ipython>=9.4.0",
]
